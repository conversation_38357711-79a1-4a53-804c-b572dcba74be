# Global Edge Infrastructure
resource "cloudflare_workers_script" "ai_inference" {
  name    = "ai-inference-edge"
  content = file("${path.module}/workers/ai-inference.js")
  
  # Deploy to 200+ edge locations globally
  compatibility_flags = ["nodejs_compat"]
}

resource "aws_eks_cluster" "regional_clusters" {
  count = length(var.regions)
  name  = "codemind-${var.regions[count.index]}"
  
  # Auto-scaling GPU clusters for AI inference
  node_groups = {
    ai_inference = {
      instance_types = ["p4d.24xlarge", "g5.48xlarge"]
      scaling_config = {
        desired_size = 10
        max_size     = 1000
        min_size     = 5
      }
    }
  }
}